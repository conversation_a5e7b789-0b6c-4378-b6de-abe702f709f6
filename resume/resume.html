<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>辜东明 - 综合简历</title>
    <style>
        :root {
            --primary-color: #0366d6;
            --secondary-color: #24292e;
            --accent-color: #f6f8fa;
            --text-color: #24292e;
            --light-text: #586069;
            --border-color: #e1e4e8;
            --highlight-bg: #fffbea;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #fff;
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
        }

        .container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
        }

        header {
            grid-column: 1 / -1;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 20px;
        }

        h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        h2 {
            font-size: 1.5rem;
            color: var(--secondary-color);
            margin: 25px 0 15px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 5px;
        }

        h3 {
            font-size: 1.2rem;
            color: var(--primary-color);
            margin: 15px 0 10px;
        }

        h4 {
            font-size: 1.1rem;
            color: var(--secondary-color);
            margin: 10px 0 5px;
        }

        p {
            margin-bottom: 10px;
        }

        ul {
            list-style-type: none;
            margin-bottom: 15px;
        }

        li {
            margin-bottom: 8px;
            position: relative;
            padding-left: 20px;
        }

        li:before {
            content: "•";
            color: var(--primary-color);
            position: absolute;
            left: 0;
        }

        .sidebar {
            background-color: var(--accent-color);
            padding: 20px;
            border-radius: 5px;
        }

        .contact-info {
            margin-bottom: 20px;
        }

        .contact-info p {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .skill-tag {
            background-color: #e1e4e8;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .job {
            margin-bottom: 25px;
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .job-title {
            font-weight: bold;
            color: var(--primary-color);
        }

        .job-date {
            color: var(--light-text);
            font-style: italic;
        }

        .project {
            margin-bottom: 15px;
            padding-left: 15px;
            border-left: 2px solid var(--border-color);
        }

        .project h4 {
            color: var(--secondary-color);
        }

        .achievements {
            margin-top: 20px;
        }

        .patent-card {
            border: 1px solid var(--border-color);
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            background-color: #ffffff;
        }

        .patent-card h4 {
            margin-top: 0;
        }

        .cert-item {
            display: inline-block;
            background-color: #e1e4e8;
            padding: 8px 15px;
            border-radius: 5px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .edu-item {
            margin-bottom: 15px;
        }

        .edu-header {
            display: flex;
            justify-content: space-between;
        }

        .edu-school {
            font-weight: bold;
        }

        .edu-date {
            color: var(--light-text);
        }

        .edu-major {
            font-style: italic;
            color: var(--secondary-color);
            margin-top: 3px;
        }

        .hobby-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .hobby-item {
            background-color: #e1e4e8;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .solo-project {
            background-color: var(--highlight-bg);
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            border: 1px solid #f0e7c2;
        }

        .solo-project h4 {
            color: #b08800;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 8px;
            margin-bottom: 10px;
        }

        .tech-item {
            font-size: 0.8rem;
            padding: 3px 8px;
            background-color: rgba(27, 31, 35, 0.08);
            border-radius: 10px;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
                font-size: 14px;
            }

            .container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            header {
                text-align: center;
                margin-bottom: 15px;
                padding-bottom: 15px;
            }

            h1 {
                font-size: 2rem;
            }

            h2 {
                font-size: 1.3rem;
                margin: 20px 0 10px;
            }

            h3 {
                font-size: 1.1rem;
            }

            .sidebar {
                padding: 15px;
                order: 2;
            }

            main {
                order: 1;
            }

            .contact-info p {
                font-size: 0.9rem;
                flex-direction: column;
                align-items: flex-start;
            }

            .skills-list {
                gap: 5px;
            }

            .skill-tag {
                font-size: 0.8rem;
                padding: 4px 8px;
            }

            .job-header {
                flex-direction: column;
                gap: 5px;
            }

            .edu-header {
                flex-direction: column;
                gap: 3px;
            }

            .hobby-list {
                gap: 8px;
            }

            .hobby-item {
                font-size: 0.8rem;
                padding: 4px 12px;
            }

            .patent-card {
                padding: 12px;
            }

            .solo-project {
                padding: 12px;
                margin: 10px 0;
            }

            .tech-stack {
                gap: 3px;
            }

            .tech-item {
                font-size: 0.7rem;
                padding: 2px 6px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 8px;
                font-size: 13px;
            }

            h1 {
                font-size: 1.8rem;
            }

            h2 {
                font-size: 1.2rem;
            }

            .sidebar {
                padding: 12px;
            }

            .contact-info p {
                font-size: 0.85rem;
            }

            .skill-tag, .hobby-item {
                font-size: 0.75rem;
                padding: 3px 6px;
            }

            .job {
                margin-bottom: 20px;
            }

            .project {
                padding-left: 10px;
                margin-bottom: 12px;
            }

            ul li {
                font-size: 0.9rem;
                line-height: 1.4;
            }
        }

        @media print {
            body {
                font-size: 12px;
                line-height: 1.4;
            }

            .container {
                grid-template-columns: 25% 75%;
                gap: 15px;
            }

            .sidebar {
                padding: 10px;
            }

            h1 {
                font-size: 18pt;
            }

            h2 {
                font-size: 14pt;
            }

            h3 {
                font-size: 12pt;
            }

            h4 {
                font-size: 11pt;
            }

            .project {
                margin-bottom: 10px;
            }
        }
    </style>
</head>

<body>
    <header>
        <h1>辜东明</h1>
        <p>资深技术专家 / AI产品经理</p>
    </header>

    <div class="container">
        <section class="sidebar">
            <div class="contact-info">
                <h2>个人信息</h2>
                <p>📱 手机: 13126483525</p>
                <p>📧 邮箱: <EMAIL></p>
                <p>🔗 github: https://github.com/LouisDM</p>
            </div>

            <div>
                <h2>教育背景</h2>
                <div class="edu-item">
                    <div class="edu-header">
                        <span class="edu-school">江南大学</span>
                        <span class="edu-date">2017- 2022</span>
                    </div>
                    <p class="edu-major">计算机科学与技术</p>
                    <p>本科学士学位</p>
                </div>

                <div class="edu-item">
                    <div class="edu-header">
                        <span class="edu-school">广州城市职业学院</span>
                        <span class="edu-date">2013- 2016</span>
                    </div>
                    <p class="edu-major">计算机应用技术</p>
                    <p>大学专科</p>
                </div>
            </div>

            <div>
                <h2>专业技能</h2>

                <h3>技术开发</h3>
                <div class="skills-list">
                    <span class="skill-tag">iOS开发</span>
                    <span class="skill-tag">Android开发</span>
                    <span class="skill-tag">Flutter</span>
                    <span class="skill-tag">AR技术</span>
                    <span class="skill-tag">AI应用开发</span>
                    <span class="skill-tag">Web3技术</span>
                </div>

                <h3>AI & AR</h3>
                <div class="skills-list">
                    <span class="skill-tag">大模型应用</span>
                    <span class="skill-tag">Lora模型训练</span>
                    <span class="skill-tag">ComfyUI</span>
                    <span class="skill-tag">自动化工具</span>
                    <span class="skill-tag">AR空间定位</span>
                    <span class="skill-tag">EasyAR</span>
                </div>

                <h3>开发语言</h3>
                <div class="skills-list">
                    <span class="skill-tag">Swift</span>
                    <span class="skill-tag">Objective-C</span>
                    <span class="skill-tag">Dart</span>
                    <span class="skill-tag">Python</span>
                    <span class="skill-tag">JavaScript</span>
                    <span class="skill-tag">PHP</span>
                </div>

                <h3>服务器运维</h3>
                <div class="skills-list">
                    <span class="skill-tag">网站搭建</span>
                    <span class="skill-tag">域名管理</span>
                    <span class="skill-tag">服务器配置</span>
                    <span class="skill-tag">Docker</span>
                </div>

                <h3>产品管理</h3>
                <div class="skills-list">
                    <span class="skill-tag">产品规划</span>
                    <span class="skill-tag">需求分析</span>
                    <span class="skill-tag">敏捷开发</span>
                    <span class="skill-tag">团队管理</span>
                    <span class="skill-tag">技术决策</span>
                </div>
            </div>

            <div>
                <h2>证书与专利</h2>
                <div class="cert-item">项目管理与系统集成工程师</div>
                <div class="patent-card">
                    <h4>发明专利</h4>
                    <p>基于AR的营销活动推广方法、装置、介质及计算机设备</p>
                    <p style="font-size: 0.9rem; color: var(--light-text);">第一发明人</p>
                    <a href="http://epub.cnipa.gov.cn/patent/CN118941333A" target="_blank"
                        style="color: var(--primary-color);">查看专利</a>
                </div>
            </div>

            <div>
                <h2>核心能力</h2>
                <ul>
                    <li>技术广度: 移动开发→AR技术→AI应用</li>
                    <li>创新能力: 新技术快速应用与落地</li>
                    <li>团队管理: 技术团队建设与管理</li>
                    <li>产品思维: 技术与产品融合能力</li>
                    <li>自我驱动: 持续学习与技术进阶</li>
                </ul>
            </div>

            <div>
                <h2>兴趣爱好</h2>
                <div class="hobby-list">
                    <span class="hobby-item">乒乓球 开球网积分1499</span>
                    <span class="hobby-item">轮滑</span>
                </div>
            </div>

            <div>
                <h2>部分项目成果展示</h2>
                <div class="hobby-list">
                    <h4>
                        <a href="https://oi7xfcfwja.feishu.cn/docx/A4Z5dVjtxoHMGQxkKALctxaenFe" target="_blank"
                            style="color: var(--primary-color);">视频/图片</a>
                    </h4>
                    <!-- <span class="hobby-item">https://oi7xfcfwja.feishu.cn/docx/A4Z5dVjtxoHMGQxkKALctxaenFe</span> -->
                </div>
            </div>

        </section>

        <main>
            <section>
                <h2>个人总结</h2>
                <p>拥有8年+移动开发与技术管理经验，在AI应用开发和AR技术领域积累了丰富的实践经验。具备从技术到产品的复合能力，能够带领团队从0到1打造创新产品。擅长AI技术边界探索与应用落地，AR空间交互设计，以及跨平台开发方案实施。持有发明专利，对新技术有浓厚兴趣并能快速学习应用。
                </p>
            </section>

            <section>
                <h2>工作经历</h2>

                <div class="job">
                    <div class="job-header">
                        <h4><span class="job-title">前海爱讯科技（深圳）有限公司广州分公司</span></h4>
                        <span class="job-date">2017.03 - 2025.06</span>
                    </div>
                    <p>职位发展: iOS开发工程师→客户端研发工程师→客户端研发主管→AIGC项目技术主管</p>

                    <div class="job">
                        <div class="job-header">
                            <span class="job-title">AIGC部门 - 全栈+产品+设计</span>
                            <span class="job-date">2024.03 - 2025.06</span>
                        </div>
                        <p>技术+产品角色，负责AI技术边界探索与评估，AI产品工具调研与应用，开源AI项目部署与优化</p>

                        <div class="project">
                            <h4>Story AI 聚合大模型创作工具</h4>
                            <h4>
                                <a href="https://infinityworks.intelliastra.com/" target="_blank"
                                    style="color: var(--primary-color);">知识库管理系统</a>
                            </h4>
                            <ul>
                                <li>设计和实现AI创作工具链，实现快速高质量内容生产</li>
                                <li>开发自动化内容采集系统，根据脚本关键词自动爬取图片视频素材</li>
                                <li>负责产品战略规划和路线图制定，优化用户体验和转化流程</li>
                                <li>规划产品功能矩阵，建立产品评估体系</li>
                            </ul>
                        </div>


                        <div class="solo-project">
                            <h4>数字人视频生成器（独立项目）</h4>
                            <p>完全独立设计和开发的全流程数字人视频生成工具，包括需求分析、UI/UX设计及前后端实现</p>
                            <div class="tech-stack">
                                <span class="tech-item">cosyVoice</span>
                                <span class="tech-item">Liveportrait</span>
                                <span class="tech-item">Easy-Wav2Lip</span>
                                <span class="tech-item">全栈开发</span>
                            </div>
                            <ul>
                                <li>设计并实现完整的数字人视频生成流程：声音合成→图片生成动态视频→声音与视频融合</li>
                                <li>集成多个AI开源工具（cosyVoice声音生成、Liveportrait动作生成、Easy-Wav2Lip视频合成）</li>
                                <li>开发直观的用户界面，简化复杂技术流程，降低使用门槛</li>
                                <li>提供丰富的自定义选项，支持多种语言文本输入、多样化头像和动作素材选择</li>
                            </ul>
                        </div>


                        <div class="project">
                            <h4>AI视频团队提效工具</h4>
                            <ul>
                                <li>搭建并优化ComfyUI工作流程，提高团队内容生产效率:25%</li>
                                <li>开发Lora模型训练流程，包括鱼子酱图片生成优化和特定场景模型训练</li>
                                <li>设计和开发短剧批量字幕提取&翻译RPA工具</li>
                                <li>转化漏斗设计与投放成本优化方案实施</li>
                            </ul>
                        </div>

                        <div class="project">
                            <h4>企业AI解决方案(半创业项目)</h4>
                            <ul>
                                <li>企业本地化大模型系统部署与调优</li>
                                <li>南方电网人资部简历挖掘智能体，处理超过1万份简历数据</li>
                                <li>南方电网施工方案审核智能体，自动化审核与知识库构建</li>
                                <li>解决数据安全与隐私保护问题，优化模型性能与响应速度</li>
                            </ul>
                        </div>
                    </div>

                    <div class="job">
                        <div class="job-header">
                            <span class="job-title">AR互动营销部门技术负责人</span>
                            <span class="job-date">2022.01 - 2024.03</span>
                        </div>
                        <p>负责技术方向规划与决策，团队建设与管理（团队规模：3人），项目流程标准化建设，以及核心技术攻关和创新项目孵化</p>

                        <div class="project">
                            <h4>大空间AR解决方案</h4>
                            <ul>
                                <li>实现EasyAR大空间定位技术和AR空间地图开发</li>
                                <li>开发多场景AR导购功能，应用于生鲜区AR导购、TrolleeAR小镇等场景</li>
                                <li>完成多个场景的AR应用落地，提升用户购物体验</li>
                            </ul>
                        </div>

                        <div class="project">
                            <h4>Trokee AR购物车</h4>
                            <ul>
                                <li>开发AR互动游戏、地图红包功能、购物大回馈游戏</li>
                                <li>设计AR小镇场景构建和月兔献礼游戏</li>
                                <li>成功落地三个AR项目，开发四个AR互动案例</li>
                            </ul>
                        </div>

                        <div class="project">
                            <h4>AR小程序与SDK</h4>
                            <ul>
                                <li>开发Android AR SDK，提供跨平台AR能力</li>
                                <li>实现小程序AR功能，扩展应用场景</li>
                                <li>整合Web AR技术，实现多端统一体验</li>
                                <li>建立完整的AR开发框架</li>
                            </ul>
                        </div>
                    </div>

                    <div class="job">
                        <div class="job-header">
                            <span class="job-title">MOGO部门 - 客户端研发</span>
                            <span class="job-date">2017.03 - 2022.01</span>
                        </div>
                        <p>负责公司移动端产品开发与技术决策，跨平台解决方案设计与实施</p>

                        <div class="project">
                            <h4>Flutter跨平台项目</h4>
                            <ul>
                                <li>负责ORB系统的多平台开发（Android/iOS/Windows/Web）</li>
                                <li>实现Dart服务端开发，确保跨平台数据一致性</li>
                                <li>优化跨平台性能，提升用户体验</li>
                            </ul>
                        </div>

                        <div class="project">
                            <h4>Web3与区块链应用<h4><a href="https://apps.apple.com/us/app/nifiti/id1634721182"
                                        target="_blank" style="color: var(--primary-color);">NiFiTi</a></h4>
                                <ul>
                                    <li>NiFiTi项目开发，包括Tea house项目和Chinese Teapot功能实现</li>
                                    <li>Web3技术应用与区块链集成</li>
                                    <li>完成10个版本迭代，实现稳定运营</li>
                                </ul>
                        </div>

                        <div class="project">
                            <h4>Mogo旅游流量设备应用</h4>
                            <h4>
                                <a href="https://apps.apple.com/us/app/mogo-global-travel-assistant/id1210741206"
                                    target="_blank" style="color: var(--primary-color);">Mogo</a>
                            </h4>

                            <ul>
                                <li>iOS应用架构设计和开发</li>
                                <li>实现多语言支持和国际化</li>
                                <li>解决应用商店上架问题</li>
                            </ul>
                        </div>

                        <div class="project">
                            <h4>ebbly旅游服务应用</h4>
                            <ul>
                                <li>iOS客户端开发</li>
                                <li>API接口设计和实现</li>
                                <li>性能优化和用户体验提升</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="job">
                    <div class="job-header">
                        <span class="job-title">广东喜到家投资管理股份有限公司</span>
                        <span class="job-date">2015.12 - 2017.03</span>
                    </div>
                    <p>独立负责iOS开发，一年时间内上线公司项目四五个。除了iOS开发外，还编写PHP接口，接触H5、Android和微信小程序开发。负责项目管理和招聘工作。</p>
                </div>

                <div class="job">
                    <div class="job-header">
                        <span class="job-title">广州博看文思科技有限公司</span>
                        <span class="job-date">2015.07 - 2015.11</span>
                    </div>
                    <p>实习期间学习iOS开发知识，参与团队完成两个小型项目。</p>
                </div>
            </section>

            <section class="achievements">
                <h2>项目亮点与成果</h2>

                <div>
                    <h3>AI应用创新</h3>
                    <ul>
                        <li>构建完整的AI创作工具链，实现内容高效生产</li>
                        <li>设计并实现特定场景的模型优化，提升生成质量</li>
                        <li>开发自动化内容处理系统，提高团队工作效率</li>
                        <li>成功部署企业级本地化大模型系统</li>
                        <li>独立开发数字人视频生成工具，实现端到端AI视频制作流程</li>
                    </ul>
                </div>

                <div>
                    <h3>AR技术突破</h3>
                    <ul>
                        <li>实现大空间AR定位技术，应用于多个商业场景</li>
                        <li>开发跨平台AR SDK，提供统一接口</li>
                        <li>融合AR与AI技术，开发智能识别系统</li>
                        <li>获得AR营销推广方法相关发明专利</li>
                    </ul>
                </div>

                <div>
                    <h3>产品管理成果</h3>
                    <ul>
                        <li>成功孵化多个AI工具产品</li>
                        <li>建立完整的产品管理体系</li>
                        <li>提升团队研发效能</li>
                        <li>优化产品运营指标</li>
                    </ul>
                </div>

                <div>
                    <h3>技术管理能力</h3>
                    <ul>
                        <li>团队管理与建设，技术方向规划</li>
                        <li>项目流程标准化，提高开发效率</li>
                        <li>创新项目孵化，技术落地能力</li>
                        <li>全栈开发能力，从移动端到服务端的技术掌握</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>
</body>

</html>
